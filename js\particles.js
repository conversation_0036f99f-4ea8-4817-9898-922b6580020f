// 江南烟雨 - 粒子系统

class ParticleSystem {
    constructor(scene) {
        this.scene = scene;
        this.rainParticles = null;
        this.mistParticles = null;
        this.rainIntensity = 0.3;
        this.mistDensity = 0.4;
        this.isTimeFreezed = false;
        
        this.init();
        this.setupEventListeners();
    }
    
    init() {
        this.createRainSystem();
        this.createMistSystem();
    }
    
    setupEventListeners() {
        eventManager.on('timeFreeze', (frozen) => {
            this.isTimeFreezed = frozen;
        });
    }
    
    createRainSystem() {
        const particleCount = 2000;
        const geometry = new THREE.BufferGeometry();
        
        // 粒子位置
        const positions = new Float32Array(particleCount * 3);
        const velocities = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        const opacities = new Float32Array(particleCount);
        
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            
            // 随机分布在场景上方
            positions[i3] = Utils.random(-50, 50);
            positions[i3 + 1] = Utils.random(20, 40);
            positions[i3 + 2] = Utils.random(-50, 50);
            
            // 下落速度
            velocities[i3] = Utils.random(-0.1, 0.1);
            velocities[i3 + 1] = Utils.random(-2, -0.5);
            velocities[i3 + 2] = Utils.random(-0.1, 0.1);
            
            // 雨滴大小
            sizes[i] = Utils.random(0.1, 0.3);
            opacities[i] = Utils.random(0.3, 0.8);
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        geometry.setAttribute('opacity', new THREE.BufferAttribute(opacities, 1));
        
        // 创建雨滴材质
        const material = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                intensity: { value: this.rainIntensity },
                color: { value: new THREE.Color(0x87CEEB) }
            },
            vertexShader: `
                attribute float size;
                attribute float opacity;
                attribute vec3 velocity;
                
                uniform float time;
                uniform float intensity;
                
                varying float vOpacity;
                
                void main() {
                    vOpacity = opacity * intensity;
                    
                    vec3 pos = position;
                    pos += velocity * time;
                    
                    // 重置超出边界的粒子
                    if (pos.y < -5.0) {
                        pos.y = 40.0;
                        pos.x = position.x + sin(time * 0.1) * 10.0;
                        pos.z = position.z + cos(time * 0.1) * 10.0;
                    }
                    
                    vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
                    gl_Position = projectionMatrix * mvPosition;
                    gl_PointSize = size * intensity * 100.0 / -mvPosition.z;
                }
            `,
            fragmentShader: `
                uniform vec3 color;
                varying float vOpacity;
                
                void main() {
                    float distanceToCenter = distance(gl_PointCoord, vec2(0.5));
                    if (distanceToCenter > 0.5) discard;
                    
                    float alpha = (1.0 - distanceToCenter * 2.0) * vOpacity;
                    gl_FragColor = vec4(color, alpha);
                }
            `,
            transparent: true,
            blending: THREE.AdditiveBlending,
            depthWrite: false
        });
        
        this.rainParticles = new THREE.Points(geometry, material);
        this.scene.add(this.rainParticles);
    }
    
    createMistSystem() {
        const particleCount = 500;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(particleCount * 3);
        const scales = new Float32Array(particleCount);
        const rotations = new Float32Array(particleCount);
        
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            
            // 雾气分布在水面附近
            positions[i3] = Utils.random(-40, 40);
            positions[i3 + 1] = Utils.random(0, 8);
            positions[i3 + 2] = Utils.random(-40, 40);
            
            scales[i] = Utils.random(2, 8);
            rotations[i] = Utils.random(0, Math.PI * 2);
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('scale', new THREE.BufferAttribute(scales, 1));
        geometry.setAttribute('rotation', new THREE.BufferAttribute(rotations, 1));
        
        // 创建雾气材质
        const material = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                density: { value: this.mistDensity },
                color: { value: new THREE.Color(0xffffff) }
            },
            vertexShader: `
                attribute float scale;
                attribute float rotation;
                
                uniform float time;
                uniform float density;
                
                varying float vOpacity;
                varying vec2 vUv;
                
                void main() {
                    vOpacity = density * 0.3;
                    vUv = uv;
                    
                    vec3 pos = position;
                    pos.x += sin(time * 0.1 + rotation) * 2.0;
                    pos.z += cos(time * 0.1 + rotation) * 2.0;
                    pos.y += sin(time * 0.05 + rotation) * 0.5;
                    
                    vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
                    gl_Position = projectionMatrix * mvPosition;
                    gl_PointSize = scale * density * 50.0 / -mvPosition.z;
                }
            `,
            fragmentShader: `
                uniform vec3 color;
                varying float vOpacity;
                
                void main() {
                    float distanceToCenter = distance(gl_PointCoord, vec2(0.5));
                    if (distanceToCenter > 0.5) discard;
                    
                    float alpha = (1.0 - distanceToCenter) * vOpacity;
                    alpha *= (1.0 - distanceToCenter); // 更柔和的边缘
                    
                    gl_FragColor = vec4(color, alpha);
                }
            `,
            transparent: true,
            blending: THREE.NormalBlending,
            depthWrite: false
        });
        
        this.mistParticles = new THREE.Points(geometry, material);
        this.scene.add(this.mistParticles);
    }
    
    setRainIntensity(intensity) {
        this.rainIntensity = intensity;
        if (this.rainParticles) {
            this.rainParticles.material.uniforms.intensity.value = intensity;
        }
    }
    
    setMistDensity(density) {
        this.mistDensity = density;
        if (this.mistParticles) {
            this.mistParticles.material.uniforms.density.value = density;
        }
    }
    
    update(deltaTime, windStrength) {
        if (this.isTimeFreezed) return;
        
        const time = performance.now() * 0.001;
        
        // 更新雨滴
        if (this.rainParticles) {
            this.rainParticles.material.uniforms.time.value = time;
            
            // 根据风力调整雨滴方向
            const positions = this.rainParticles.geometry.attributes.position.array;
            const velocities = this.rainParticles.geometry.attributes.velocity.array;
            
            for (let i = 0; i < positions.length; i += 3) {
                // 添加风力影响
                positions[i] += velocities[i] * deltaTime + windStrength * 0.1;
                positions[i + 1] += velocities[i + 1] * deltaTime;
                positions[i + 2] += velocities[i + 2] * deltaTime + windStrength * 0.05;
                
                // 重置超出边界的粒子
                if (positions[i + 1] < -5) {
                    positions[i] = Utils.random(-50, 50);
                    positions[i + 1] = Utils.random(20, 40);
                    positions[i + 2] = Utils.random(-50, 50);
                }
            }
            
            this.rainParticles.geometry.attributes.position.needsUpdate = true;
        }
        
        // 更新雾气
        if (this.mistParticles) {
            this.mistParticles.material.uniforms.time.value = time;
            
            // 雾气随风飘动
            const positions = this.mistParticles.geometry.attributes.position.array;
            const rotations = this.mistParticles.geometry.attributes.rotation.array;
            
            for (let i = 0; i < positions.length; i += 3) {
                const index = i / 3;
                positions[i] += Math.sin(time * 0.1 + rotations[index]) * windStrength * 0.02;
                positions[i + 2] += Math.cos(time * 0.1 + rotations[index]) * windStrength * 0.02;
            }
            
            this.mistParticles.geometry.attributes.position.needsUpdate = true;
        }
    }
    
    dispose() {
        if (this.rainParticles) {
            this.scene.remove(this.rainParticles);
            this.rainParticles.geometry.dispose();
            this.rainParticles.material.dispose();
        }
        
        if (this.mistParticles) {
            this.scene.remove(this.mistParticles);
            this.mistParticles.geometry.dispose();
            this.mistParticles.material.dispose();
        }
    }
}
