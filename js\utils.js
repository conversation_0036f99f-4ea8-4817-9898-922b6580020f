// 江南烟雨 - 工具函数

class Utils {
    // 线性插值
    static lerp(start, end, factor) {
        return start + (end - start) * factor;
    }
    
    // 平滑步进函数
    static smoothstep(min, max, value) {
        const x = Math.max(0, Math.min(1, (value - min) / (max - min)));
        return x * x * (3 - 2 * x);
    }
    
    // 随机数生成
    static random(min, max) {
        return Math.random() * (max - min) + min;
    }
    
    // 噪声函数（简化版）
    static noise(x, y, z = 0) {
        const n = Math.sin(x * 12.9898 + y * 78.233 + z * 37.719) * 43758.5453;
        return (n - Math.floor(n)) * 2 - 1;
    }
    
    // 创建材质的通用方法
    static createMaterial(options = {}) {
        const defaultOptions = {
            transparent: true,
            opacity: 0.8,
            side: THREE.DoubleSide
        };
        
        return new THREE.MeshLambertMaterial({
            ...defaultOptions,
            ...options
        });
    }
    
    // 创建几何体的辅助方法
    static createGeometry(type, ...params) {
        switch(type) {
            case 'plane':
                return new THREE.PlaneGeometry(...params);
            case 'box':
                return new THREE.BoxGeometry(...params);
            case 'sphere':
                return new THREE.SphereGeometry(...params);
            case 'cylinder':
                return new THREE.CylinderGeometry(...params);
            default:
                return new THREE.BoxGeometry(1, 1, 1);
        }
    }
    
    // 颜色混合
    static mixColors(color1, color2, factor) {
        const c1 = new THREE.Color(color1);
        const c2 = new THREE.Color(color2);
        return c1.lerp(c2, factor);
    }
    
    // 获取鼠标在3D空间中的位置
    static getMousePosition(event, camera, renderer) {
        const mouse = new THREE.Vector2();
        mouse.x = (event.clientX / renderer.domElement.clientWidth) * 2 - 1;
        mouse.y = -(event.clientY / renderer.domElement.clientHeight) * 2 + 1;
        
        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, camera);
        
        return { mouse, raycaster };
    }
    
    // 创建文字纹理
    static createTextTexture(text, options = {}) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        
        const fontSize = options.fontSize || 32;
        const fontFamily = options.fontFamily || 'Microsoft YaHei';
        const color = options.color || '#ffffff';
        const backgroundColor = options.backgroundColor || 'transparent';
        
        canvas.width = options.width || 512;
        canvas.height = options.height || 256;
        
        // 设置背景
        if (backgroundColor !== 'transparent') {
            context.fillStyle = backgroundColor;
            context.fillRect(0, 0, canvas.width, canvas.height);
        }
        
        // 设置文字样式
        context.font = `${fontSize}px ${fontFamily}`;
        context.fillStyle = color;
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        
        // 绘制文字
        const lines = text.split('\n');
        const lineHeight = fontSize * 1.2;
        const startY = canvas.height / 2 - (lines.length - 1) * lineHeight / 2;
        
        lines.forEach((line, index) => {
            context.fillText(line, canvas.width / 2, startY + index * lineHeight);
        });
        
        const texture = new THREE.CanvasTexture(canvas);
        texture.needsUpdate = true;
        
        return texture;
    }
    
    // 动画缓动函数
    static easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    }
    
    static easeOutElastic(t) {
        const c4 = (2 * Math.PI) / 3;
        return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
    }
    
    // 性能监控
    static createPerformanceMonitor() {
        const stats = {
            fps: 0,
            frameCount: 0,
            lastTime: performance.now()
        };
        
        return {
            update: () => {
                stats.frameCount++;
                const currentTime = performance.now();
                if (currentTime - stats.lastTime >= 1000) {
                    stats.fps = Math.round((stats.frameCount * 1000) / (currentTime - stats.lastTime));
                    stats.frameCount = 0;
                    stats.lastTime = currentTime;
                }
                return stats.fps;
            },
            getFPS: () => stats.fps
        };
    }
}

// 事件管理器
class EventManager {
    constructor() {
        this.events = {};
    }
    
    on(eventName, callback) {
        if (!this.events[eventName]) {
            this.events[eventName] = [];
        }
        this.events[eventName].push(callback);
    }
    
    off(eventName, callback) {
        if (this.events[eventName]) {
            this.events[eventName] = this.events[eventName].filter(cb => cb !== callback);
        }
    }
    
    emit(eventName, data) {
        if (this.events[eventName]) {
            this.events[eventName].forEach(callback => callback(data));
        }
    }
}

// 全局事件管理器实例
window.eventManager = new EventManager();
