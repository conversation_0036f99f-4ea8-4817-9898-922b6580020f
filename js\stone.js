// 江南烟雨 - 三生石系统

class StoneSystem {
    constructor(scene, camera, renderer) {
        this.scene = scene;
        this.camera = camera;
        this.renderer = renderer;
        this.stoneMesh = null;
        this.inscriptions = [];
        this.isHovered = false;
        this.glowEffect = null;
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        
        // 回调函数
        this.onStoneClick = null;
        
        this.init();
        this.setupEventListeners();
    }
    
    init() {
        this.createStone();
        this.createGlowEffect();
        this.createInscriptionSurface();
    }
    
    setupEventListeners() {
        window.addEventListener('mousemove', (event) => this.onMouseMove(event));
        window.addEventListener('click', (event) => this.onMouseClick(event));
    }
    
    createStone() {
        // 创建不规则的石头形状
        const stoneGeometry = new THREE.SphereGeometry(2, 16, 12);
        
        // 随机变形，使其看起来更自然
        const positions = stoneGeometry.attributes.position.array;
        for (let i = 0; i < positions.length; i += 3) {
            const vertex = new THREE.Vector3(positions[i], positions[i + 1], positions[i + 2]);
            const noise = Utils.noise(vertex.x * 0.5, vertex.y * 0.5, vertex.z * 0.5);
            
            vertex.multiplyScalar(1 + noise * 0.3);
            positions[i] = vertex.x;
            positions[i + 1] = vertex.y;
            positions[i + 2] = vertex.z;
        }
        stoneGeometry.attributes.position.needsUpdate = true;
        stoneGeometry.computeVertexNormals();
        
        // 创建石头材质
        const stoneMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                hoverIntensity: { value: 0 },
                stoneColor: { value: new THREE.Color(0x8B7355) },
                glowColor: { value: new THREE.Color(0x87CEEB) },
                inscriptionTexture: { value: null }
            },
            vertexShader: `
                uniform float time;
                uniform float hoverIntensity;
                
                varying vec3 vPosition;
                varying vec3 vNormal;
                varying vec2 vUv;
                
                void main() {
                    vPosition = position;
                    vNormal = normal;
                    vUv = uv;
                    
                    vec3 pos = position;
                    
                    // 悬停时的轻微脉动效果
                    pos += normal * sin(time * 3.0) * 0.02 * hoverIntensity;
                    
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform float hoverIntensity;
                uniform vec3 stoneColor;
                uniform vec3 glowColor;
                uniform sampler2D inscriptionTexture;
                
                varying vec3 vPosition;
                varying vec3 vNormal;
                varying vec2 vUv;
                
                void main() {
                    vec3 color = stoneColor;
                    
                    // 基础石头纹理
                    float noise = sin(vPosition.x * 10.0) * sin(vPosition.y * 10.0) * sin(vPosition.z * 10.0);
                    color += noise * 0.1;
                    
                    // 悬停时的发光效果
                    vec3 glow = glowColor * hoverIntensity * 0.5;
                    color = mix(color, color + glow, hoverIntensity);
                    
                    // 铭文纹理
                    if (inscriptionTexture != null) {
                        vec4 inscription = texture2D(inscriptionTexture, vUv);
                        color = mix(color, inscription.rgb, inscription.a * 0.8);
                    }
                    
                    // 边缘光效
                    float fresnel = pow(1.0 - abs(dot(normalize(vNormal), vec3(0, 0, 1))), 2.0);
                    color += glowColor * fresnel * hoverIntensity * 0.3;
                    
                    gl_FragColor = vec4(color, 1.0);
                }
            `
        });
        
        this.stoneMesh = new THREE.Mesh(stoneGeometry, stoneMaterial);
        this.stoneMesh.position.set(0, 1, 0);
        this.stoneMesh.castShadow = true;
        this.stoneMesh.receiveShadow = true;
        
        // 添加用户数据，用于射线检测
        this.stoneMesh.userData = { type: 'stone' };
        
        this.scene.add(this.stoneMesh);
    }
    
    createGlowEffect() {
        // 创建外层发光效果
        const glowGeometry = new THREE.SphereGeometry(2.5, 16, 12);
        const glowMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                intensity: { value: 0 },
                color: { value: new THREE.Color(0x87CEEB) }
            },
            vertexShader: `
                uniform float time;
                varying vec3 vNormal;
                varying vec3 vPosition;
                
                void main() {
                    vNormal = normal;
                    vPosition = position;
                    
                    vec3 pos = position;
                    pos += normal * sin(time * 2.0) * 0.1;
                    
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                }
            `,
            fragmentShader: `
                uniform float intensity;
                uniform vec3 color;
                varying vec3 vNormal;
                
                void main() {
                    float fresnel = pow(1.0 - abs(dot(normalize(vNormal), vec3(0, 0, 1))), 3.0);
                    float alpha = fresnel * intensity * 0.5;
                    
                    gl_FragColor = vec4(color, alpha);
                }
            `,
            transparent: true,
            blending: THREE.AdditiveBlending,
            side: THREE.BackSide
        });
        
        this.glowEffect = new THREE.Mesh(glowGeometry, glowMaterial);
        this.glowEffect.position.copy(this.stoneMesh.position);
        this.scene.add(this.glowEffect);
    }
    
    createInscriptionSurface() {
        // 创建一个平面用于显示铭文
        this.inscriptionCanvas = document.createElement('canvas');
        this.inscriptionCanvas.width = 512;
        this.inscriptionCanvas.height = 512;
        this.inscriptionContext = this.inscriptionCanvas.getContext('2d');
        
        this.inscriptionTexture = new THREE.CanvasTexture(this.inscriptionCanvas);
        this.inscriptionTexture.needsUpdate = true;
    }
    
    onMouseMove(event) {
        // 更新鼠标位置
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        
        // 射线检测
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObject(this.stoneMesh);
        
        const wasHovered = this.isHovered;
        this.isHovered = intersects.length > 0;
        
        // 悬停状态改变时的处理
        if (this.isHovered !== wasHovered) {
            if (this.isHovered) {
                this.onStoneHover();
            } else {
                this.onStoneLeave();
            }
        }
    }
    
    onMouseClick(event) {
        if (this.isHovered && this.onStoneClick) {
            this.onStoneClick();
        }
    }
    
    onStoneHover() {
        // 改变鼠标样式
        document.body.style.cursor = 'pointer';
        
        // 开始发光动画
        this.animateGlow(true);
    }
    
    onStoneLeave() {
        // 恢复鼠标样式
        document.body.style.cursor = 'default';
        
        // 停止发光动画
        this.animateGlow(false);
    }
    
    animateGlow(enable) {
        const targetIntensity = enable ? 1.0 : 0.0;
        const duration = 500; // 毫秒
        const startTime = performance.now();
        const startIntensity = this.stoneMesh.material.uniforms.hoverIntensity.value;
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const easedProgress = Utils.easeInOutCubic(progress);
            
            const currentIntensity = Utils.lerp(startIntensity, targetIntensity, easedProgress);
            
            this.stoneMesh.material.uniforms.hoverIntensity.value = currentIntensity;
            this.glowEffect.material.uniforms.intensity.value = currentIntensity;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    inscribeText(text) {
        // 清空画布
        this.inscriptionContext.clearRect(0, 0, 512, 512);
        
        // 设置文字样式
        this.inscriptionContext.fillStyle = '#ffffff';
        this.inscriptionContext.font = '24px Microsoft YaHei';
        this.inscriptionContext.textAlign = 'center';
        this.inscriptionContext.textBaseline = 'middle';
        
        // 添加阴影效果
        this.inscriptionContext.shadowColor = '#000000';
        this.inscriptionContext.shadowBlur = 4;
        this.inscriptionContext.shadowOffsetX = 2;
        this.inscriptionContext.shadowOffsetY = 2;
        
        // 分行显示文字
        const lines = text.split('\n');
        const lineHeight = 30;
        const startY = 256 - (lines.length - 1) * lineHeight / 2;
        
        lines.forEach((line, index) => {
            this.inscriptionContext.fillText(line, 256, startY + index * lineHeight);
        });
        
        // 更新纹理
        this.inscriptionTexture.needsUpdate = true;
        this.stoneMesh.material.uniforms.inscriptionTexture.value = this.inscriptionTexture;
        
        // 播放刻印动画
        this.playInscriptionAnimation(text);
    }
    
    playInscriptionAnimation(text) {
        // 创建文字刻印的动画效果
        const duration = 2000;
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用弹性缓动函数
            const easedProgress = Utils.easeOutElastic(progress);
            
            // 更新石头的脉动效果
            const pulseIntensity = Math.sin(progress * Math.PI * 4) * (1 - progress);
            this.stoneMesh.material.uniforms.hoverIntensity.value = pulseIntensity;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // 动画完成，保存铭文
                this.inscriptions.push({
                    text: text,
                    timestamp: new Date(),
                    texture: this.inscriptionTexture.clone()
                });
                
                // 重置发光效果
                this.stoneMesh.material.uniforms.hoverIntensity.value = 0;
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    update(deltaTime) {
        const time = performance.now() * 0.001;
        
        // 更新着色器时间
        if (this.stoneMesh) {
            this.stoneMesh.material.uniforms.time.value = time;
        }
        
        if (this.glowEffect) {
            this.glowEffect.material.uniforms.time.value = time;
        }
    }
    
    // 获取所有铭文
    getInscriptions() {
        return this.inscriptions;
    }
    
    // 清除所有铭文
    clearInscriptions() {
        this.inscriptions = [];
        this.stoneMesh.material.uniforms.inscriptionTexture.value = null;
        
        // 清空画布
        this.inscriptionContext.clearRect(0, 0, 512, 512);
        this.inscriptionTexture.needsUpdate = true;
    }
    
    dispose() {
        if (this.stoneMesh) {
            this.scene.remove(this.stoneMesh);
            this.stoneMesh.geometry.dispose();
            this.stoneMesh.material.dispose();
        }
        
        if (this.glowEffect) {
            this.scene.remove(this.glowEffect);
            this.glowEffect.geometry.dispose();
            this.glowEffect.material.dispose();
        }
        
        if (this.inscriptionTexture) {
            this.inscriptionTexture.dispose();
        }
    }
}
