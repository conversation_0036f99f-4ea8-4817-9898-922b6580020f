/* 江南烟雨 - 样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
    height: 100vh;
}

#container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#three-canvas {
    display: block;
    width: 100%;
    height: 100%;
}

/* 控制面板样式 */
#controls-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    min-width: 250px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.control-group {
    margin-bottom: 15px;
}

.control-group label {
    display: block;
    color: white;
    font-size: 14px;
    margin-bottom: 8px;
    font-weight: 500;
}

.control-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.3);
    outline: none;
    -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.control-group span {
    color: white;
    font-size: 12px;
    float: right;
    margin-top: 5px;
}

/* 三生石文字输入面板 */
#inscription-panel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    min-width: 400px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

#inscription-panel.hidden {
    display: none;
}

.panel-content h3 {
    color: #f0f0f0;
    text-align: center;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 300;
}

#inscription-text {
    width: 100%;
    height: 120px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 15px;
    color: white;
    font-size: 14px;
    resize: none;
    outline: none;
    font-family: inherit;
}

#inscription-text::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.panel-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.panel-buttons button {
    padding: 10px 25px;
    border: none;
    border-radius: 25px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
}

#inscribe-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

#cancel-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.panel-buttons button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* 加载屏幕 */
#loading-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2c3e50, #3498db);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    transition: opacity 1s ease-out;
}

#loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-content h2 {
    font-size: 36px;
    margin-bottom: 20px;
    font-weight: 300;
    letter-spacing: 3px;
}

.loading-content p {
    font-size: 16px;
    margin-bottom: 30px;
    opacity: 0.8;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 信息面板 */
#info-panel {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-item {
    display: flex;
    align-items: center;
    color: white;
    font-size: 12px;
    margin-bottom: 8px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item .icon {
    margin-right: 8px;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #controls-panel {
        top: 10px;
        right: 10px;
        min-width: 200px;
        padding: 15px;
    }
    
    #inscription-panel {
        min-width: 90vw;
        padding: 20px;
    }
    
    #info-panel {
        bottom: 10px;
        left: 10px;
        padding: 10px;
    }
    
    .info-item {
        font-size: 11px;
    }
}
