// 江南烟雨 - 水面系统

class WaterSystem {
    constructor(scene) {
        this.scene = scene;
        this.waterMesh = null;
        this.ripples = [];
        this.isTimeFreezed = false;
        this.time = 0;
        
        this.init();
        this.setupEventListeners();
    }
    
    init() {
        this.createWaterSurface();
        this.createRippleSystem();
    }
    
    setupEventListeners() {
        eventManager.on('timeFreeze', (frozen) => {
            this.isTimeFreezed = frozen;
        });
    }
    
    createWaterSurface() {
        const geometry = new THREE.PlaneGeometry(100, 100, 128, 128);
        
        // 创建水面着色器材质
        const material = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                windStrength: { value: 0.2 },
                rippleData: { value: [] },
                waterColor: { value: new THREE.Color(0x006994) },
                foamColor: { value: new THREE.Color(0xffffff) },
                opacity: { value: 0.8 }
            },
            vertexShader: `
                uniform float time;
                uniform float windStrength;
                uniform vec4 rippleData[10]; // x, z, radius, strength
                
                varying vec2 vUv;
                varying vec3 vPosition;
                varying float vElevation;
                
                // 简化的噪声函数
                float noise(vec2 p) {
                    return sin(p.x * 12.9898 + p.y * 78.233) * 43758.5453;
                }
                
                float wave(vec2 pos, float time, float frequency, float amplitude) {
                    return sin(pos.x * frequency + time) * sin(pos.y * frequency + time) * amplitude;
                }
                
                void main() {
                    vUv = uv;
                    vPosition = position;
                    
                    vec3 pos = position;
                    float elevation = 0.0;
                    
                    // 基础波浪
                    elevation += wave(pos.xz, time * 0.5, 0.1, 0.3);
                    elevation += wave(pos.xz, time * 0.3, 0.2, 0.2);
                    elevation += wave(pos.xz, time * 0.7, 0.05, 0.5);
                    
                    // 风力影响
                    elevation += sin(pos.x * 0.1 + time * windStrength) * windStrength * 0.2;
                    elevation += cos(pos.z * 0.1 + time * windStrength) * windStrength * 0.15;
                    
                    // 涟漪效果
                    for (int i = 0; i < 10; i++) {
                        vec4 ripple = rippleData[i];
                        if (ripple.w > 0.0) {
                            float dist = distance(pos.xz, ripple.xy);
                            if (dist < ripple.z) {
                                float rippleEffect = sin((dist - time * 2.0) * 10.0) * 
                                                   exp(-dist * 0.1) * ripple.w;
                                elevation += rippleEffect * 0.1;
                            }
                        }
                    }
                    
                    pos.y += elevation;
                    vElevation = elevation;
                    
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                }
            `,
            fragmentShader: `
                uniform vec3 waterColor;
                uniform vec3 foamColor;
                uniform float opacity;
                
                varying vec2 vUv;
                varying vec3 vPosition;
                varying float vElevation;
                
                void main() {
                    // 基础水色
                    vec3 color = waterColor;
                    
                    // 根据高度变化添加泡沫效果
                    float foam = smoothstep(0.1, 0.3, abs(vElevation));
                    color = mix(color, foamColor, foam * 0.3);
                    
                    // 添加深度感
                    float depth = 1.0 - smoothstep(0.0, 50.0, length(vPosition.xz));
                    color = mix(color * 0.5, color, depth);
                    
                    // 添加反射效果
                    float fresnel = pow(1.0 - abs(dot(normalize(vPosition), vec3(0, 1, 0))), 2.0);
                    color = mix(color, vec3(0.8, 0.9, 1.0), fresnel * 0.2);
                    
                    gl_FragColor = vec4(color, opacity);
                }
            `,
            transparent: true,
            side: THREE.DoubleSide
        });
        
        this.waterMesh = new THREE.Mesh(geometry, material);
        this.waterMesh.rotation.x = -Math.PI / 2;
        this.waterMesh.position.y = 0;
        this.waterMesh.receiveShadow = true;
        
        this.scene.add(this.waterMesh);
    }
    
    createRippleSystem() {
        // 定期创建随机涟漪，模拟雨滴落水
        setInterval(() => {
            if (!this.isTimeFreezed && this.ripples.length < 10) {
                this.addRipple(
                    Utils.random(-30, 30),
                    Utils.random(-30, 30),
                    Utils.random(0.5, 1.0)
                );
            }
        }, 2000);
    }
    
    addRipple(x, z, strength = 1.0) {
        const ripple = {
            x: x,
            z: z,
            radius: 0,
            maxRadius: 15,
            strength: strength,
            speed: 2.0,
            life: 1.0,
            maxLife: 1.0
        };
        
        this.ripples.push(ripple);
        
        // 限制涟漪数量
        if (this.ripples.length > 10) {
            this.ripples.shift();
        }
    }
    
    // 创建"圈圈圆圆圈圈"的视觉效果
    createCircularRipples() {
        const centerX = 0;
        const centerZ = 0;
        const numCircles = 5;
        
        for (let i = 0; i < numCircles; i++) {
            setTimeout(() => {
                if (!this.isTimeFreezed) {
                    const angle = (i / numCircles) * Math.PI * 2;
                    const radius = 8 + i * 3;
                    const x = centerX + Math.cos(angle) * radius;
                    const z = centerZ + Math.sin(angle) * radius;
                    this.addRipple(x, z, 0.8);
                }
            }, i * 500);
        }
    }
    
    update(deltaTime, windStrength) {
        if (this.isTimeFreezed) return;
        
        this.time += deltaTime;
        
        // 更新水面着色器
        if (this.waterMesh) {
            this.waterMesh.material.uniforms.time.value = this.time;
            this.waterMesh.material.uniforms.windStrength.value = windStrength;
        }
        
        // 更新涟漪
        this.updateRipples(deltaTime);
        
        // 定期创建圆形涟漪效果
        if (Math.random() < 0.001) {
            this.createCircularRipples();
        }
    }
    
    updateRipples(deltaTime) {
        // 更新涟漪状态
        for (let i = this.ripples.length - 1; i >= 0; i--) {
            const ripple = this.ripples[i];
            
            ripple.radius += ripple.speed * deltaTime;
            ripple.life -= deltaTime / ripple.maxLife;
            
            // 移除消失的涟漪
            if (ripple.life <= 0 || ripple.radius > ripple.maxRadius) {
                this.ripples.splice(i, 1);
            }
        }
        
        // 更新着色器中的涟漪数据
        if (this.waterMesh) {
            const rippleData = [];
            for (let i = 0; i < 10; i++) {
                if (i < this.ripples.length) {
                    const ripple = this.ripples[i];
                    rippleData.push(
                        ripple.x,
                        ripple.z,
                        ripple.radius,
                        ripple.strength * ripple.life
                    );
                } else {
                    rippleData.push(0, 0, 0, 0);
                }
            }
            
            // 将数据转换为Vector4数组
            const rippleVectors = [];
            for (let i = 0; i < rippleData.length; i += 4) {
                rippleVectors.push(new THREE.Vector4(
                    rippleData[i],
                    rippleData[i + 1],
                    rippleData[i + 2],
                    rippleData[i + 3]
                ));
            }
            
            this.waterMesh.material.uniforms.rippleData.value = rippleVectors;
        }
    }
    
    // 在指定位置创建涟漪（用于雨滴交互）
    createRippleAt(x, z, strength = 1.0) {
        this.addRipple(x, z, strength);
    }
    
    dispose() {
        if (this.waterMesh) {
            this.scene.remove(this.waterMesh);
            this.waterMesh.geometry.dispose();
            this.waterMesh.material.dispose();
        }
    }
}
