// 江南烟雨 - 环境建模系统

class Environment {
    constructor(scene) {
        this.scene = scene;
        this.willowTrees = [];
        this.buildings = [];
        this.bridges = [];
        this.isTimeFreezed = false;
        this.time = 0;
        
        this.init();
        this.setupEventListeners();
    }
    
    init() {
        this.createWillowTrees();
        this.createTraditionalBuildings();
        this.createBridges();
        this.createLotusLeaves();
        this.createRocks();
    }
    
    setupEventListeners() {
        eventManager.on('timeFreeze', (frozen) => {
            this.isTimeFreezed = frozen;
        });
    }
    
    createWillowTrees() {
        const treePositions = [
            { x: -25, z: 15 },
            { x: 20, z: -18 },
            { x: -15, z: -25 },
            { x: 30, z: 20 },
            { x: -35, z: -10 }
        ];
        
        treePositions.forEach(pos => {
            const tree = this.createSingleWillowTree();
            tree.position.set(pos.x, 0, pos.z);
            tree.scale.setScalar(Utils.random(0.8, 1.2));
            this.scene.add(tree);
            this.willowTrees.push(tree);
        });
    }
    
    createSingleWillowTree() {
        const treeGroup = new THREE.Group();
        
        // 树干
        const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.8, 8, 8);
        const trunkMaterial = Utils.createMaterial({
            color: 0x4a4a4a,
            roughness: 0.8
        });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 4;
        trunk.castShadow = true;
        treeGroup.add(trunk);
        
        // 柳条
        const branchCount = 12;
        for (let i = 0; i < branchCount; i++) {
            const branch = this.createWillowBranch();
            const angle = (i / branchCount) * Math.PI * 2;
            const radius = Utils.random(1, 2);
            
            branch.position.set(
                Math.cos(angle) * radius,
                Utils.random(6, 8),
                Math.sin(angle) * radius
            );
            branch.rotation.y = angle;
            
            treeGroup.add(branch);
        }
        
        return treeGroup;
    }
    
    createWillowBranch() {
        const branchGroup = new THREE.Group();
        const segmentCount = 8;
        
        for (let i = 0; i < segmentCount; i++) {
            const segmentGeometry = new THREE.CylinderGeometry(
                0.02 * (1 - i / segmentCount),
                0.05 * (1 - i / segmentCount),
                1,
                4
            );
            
            const segmentMaterial = Utils.createMaterial({
                color: 0x228B22,
                transparent: true,
                opacity: 0.8
            });
            
            const segment = new THREE.Mesh(segmentGeometry, segmentMaterial);
            segment.position.y = -i * 0.8;
            segment.position.x = Math.sin(i * 0.3) * 0.2;
            segment.rotation.z = Math.sin(i * 0.5) * 0.2;
            
            // 添加叶子
            if (i > 2) {
                const leafCount = 3;
                for (let j = 0; j < leafCount; j++) {
                    const leaf = this.createLeaf();
                    leaf.position.set(
                        Utils.random(-0.3, 0.3),
                        Utils.random(-0.2, 0.2),
                        Utils.random(-0.3, 0.3)
                    );
                    segment.add(leaf);
                }
            }
            
            branchGroup.add(segment);
        }
        
        return branchGroup;
    }
    
    createLeaf() {
        const leafGeometry = new THREE.PlaneGeometry(0.1, 0.3);
        const leafMaterial = Utils.createMaterial({
            color: 0x90EE90,
            transparent: true,
            opacity: 0.7,
            side: THREE.DoubleSide
        });
        
        const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
        leaf.rotation.x = Utils.random(-0.5, 0.5);
        leaf.rotation.y = Utils.random(0, Math.PI * 2);
        
        return leaf;
    }
    
    createTraditionalBuildings() {
        const buildingPositions = [
            { x: -40, z: -30, type: 'pavilion' },
            { x: 35, z: 35, type: 'house' },
            { x: -30, z: 40, type: 'tower' }
        ];
        
        buildingPositions.forEach(pos => {
            let building;
            switch(pos.type) {
                case 'pavilion':
                    building = this.createPavilion();
                    break;
                case 'house':
                    building = this.createTraditionalHouse();
                    break;
                case 'tower':
                    building = this.createPagoda();
                    break;
            }
            
            building.position.set(pos.x, 0, pos.z);
            this.scene.add(building);
            this.buildings.push(building);
        });
    }
    
    createPavilion() {
        const pavilionGroup = new THREE.Group();
        
        // 基座
        const baseGeometry = new THREE.CylinderGeometry(4, 4, 0.5, 8);
        const baseMaterial = Utils.createMaterial({ color: 0x8B4513 });
        const base = new THREE.Mesh(baseGeometry, baseMaterial);
        base.position.y = 0.25;
        base.receiveShadow = true;
        pavilionGroup.add(base);
        
        // 柱子
        for (let i = 0; i < 6; i++) {
            const angle = (i / 6) * Math.PI * 2;
            const pillarGeometry = new THREE.CylinderGeometry(0.15, 0.15, 4, 8);
            const pillarMaterial = Utils.createMaterial({ color: 0xA0522D });
            const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial);
            
            pillar.position.set(
                Math.cos(angle) * 3,
                2.5,
                Math.sin(angle) * 3
            );
            pillar.castShadow = true;
            pavilionGroup.add(pillar);
        }
        
        // 屋顶
        const roofGeometry = new THREE.ConeGeometry(5, 2, 8);
        const roofMaterial = Utils.createMaterial({ color: 0x8B0000 });
        const roof = new THREE.Mesh(roofGeometry, roofMaterial);
        roof.position.y = 5.5;
        roof.castShadow = true;
        pavilionGroup.add(roof);
        
        return pavilionGroup;
    }
    
    createTraditionalHouse() {
        const houseGroup = new THREE.Group();
        
        // 主体建筑
        const wallGeometry = new THREE.BoxGeometry(6, 4, 8);
        const wallMaterial = Utils.createMaterial({ color: 0xF5F5DC });
        const walls = new THREE.Mesh(wallGeometry, wallMaterial);
        walls.position.y = 2;
        walls.castShadow = true;
        walls.receiveShadow = true;
        houseGroup.add(walls);
        
        // 屋顶
        const roofGeometry = new THREE.CylinderGeometry(0, 5, 3, 4);
        const roofMaterial = Utils.createMaterial({ color: 0x696969 });
        const roof = new THREE.Mesh(roofGeometry, roofMaterial);
        roof.position.y = 5.5;
        roof.rotation.y = Math.PI / 4;
        roof.castShadow = true;
        houseGroup.add(roof);
        
        return houseGroup;
    }
    
    createPagoda() {
        const pagodaGroup = new THREE.Group();
        
        // 多层塔身
        for (let i = 0; i < 5; i++) {
            const size = 3 - i * 0.4;
            const height = 2;
            
            // 塔身
            const bodyGeometry = new THREE.CylinderGeometry(size, size, height, 8);
            const bodyMaterial = Utils.createMaterial({ color: 0xDEB887 });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.position.y = i * height + height / 2;
            body.castShadow = true;
            pagodaGroup.add(body);
            
            // 屋檐
            const eaveGeometry = new THREE.CylinderGeometry(size + 0.5, size + 0.3, 0.3, 8);
            const eaveMaterial = Utils.createMaterial({ color: 0x8B4513 });
            const eave = new THREE.Mesh(eaveGeometry, eaveMaterial);
            eave.position.y = i * height + height;
            eave.castShadow = true;
            pagodaGroup.add(eave);
        }
        
        return pagodaGroup;
    }
    
    createBridges() {
        const bridge = this.createArchBridge();
        bridge.position.set(0, 0, -15);
        bridge.rotation.y = Math.PI / 4;
        this.scene.add(bridge);
        this.bridges.push(bridge);
    }
    
    createArchBridge() {
        const bridgeGroup = new THREE.Group();
        
        // 桥面
        const deckGeometry = new THREE.BoxGeometry(12, 0.5, 2);
        const deckMaterial = Utils.createMaterial({ color: 0x8B4513 });
        const deck = new THREE.Mesh(deckGeometry, deckMaterial);
        deck.position.y = 2;
        deck.castShadow = true;
        bridgeGroup.add(deck);
        
        // 桥拱
        const archGeometry = new THREE.TorusGeometry(5, 0.5, 8, 16, Math.PI);
        const archMaterial = Utils.createMaterial({ color: 0x696969 });
        const arch = new THREE.Mesh(archGeometry, archMaterial);
        arch.rotation.z = Math.PI;
        arch.position.y = 2;
        arch.castShadow = true;
        bridgeGroup.add(arch);
        
        return bridgeGroup;
    }
    
    createLotusLeaves() {
        for (let i = 0; i < 15; i++) {
            const leaf = this.createLotusLeaf();
            leaf.position.set(
                Utils.random(-20, 20),
                0.1,
                Utils.random(-20, 20)
            );
            this.scene.add(leaf);
        }
    }
    
    createLotusLeaf() {
        const leafGeometry = new THREE.CircleGeometry(Utils.random(1, 2), 16);
        const leafMaterial = Utils.createMaterial({
            color: 0x228B22,
            transparent: true,
            opacity: 0.8,
            side: THREE.DoubleSide
        });
        
        const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
        leaf.rotation.x = -Math.PI / 2;
        leaf.rotation.z = Utils.random(0, Math.PI * 2);
        
        return leaf;
    }
    
    createRocks() {
        for (let i = 0; i < 8; i++) {
            const rock = this.createRock();
            rock.position.set(
                Utils.random(-40, 40),
                0,
                Utils.random(-40, 40)
            );
            this.scene.add(rock);
        }
    }
    
    createRock() {
        const rockGeometry = new THREE.SphereGeometry(
            Utils.random(0.5, 1.5),
            8,
            6
        );
        
        // 随机变形
        const positions = rockGeometry.attributes.position.array;
        for (let i = 0; i < positions.length; i += 3) {
            positions[i] += Utils.random(-0.2, 0.2);
            positions[i + 1] += Utils.random(-0.2, 0.2);
            positions[i + 2] += Utils.random(-0.2, 0.2);
        }
        rockGeometry.attributes.position.needsUpdate = true;
        
        const rockMaterial = Utils.createMaterial({ color: 0x696969 });
        const rock = new THREE.Mesh(rockGeometry, rockMaterial);
        rock.castShadow = true;
        rock.receiveShadow = true;
        
        return rock;
    }
    
    update(deltaTime, windStrength) {
        if (this.isTimeFreezed) return;
        
        this.time += deltaTime;
        
        // 更新柳条摆动
        this.willowTrees.forEach(tree => {
            tree.children.forEach(child => {
                if (child.children && child.children.length > 0) {
                    // 这是柳条分支
                    child.children.forEach((segment, index) => {
                        const swayAmount = windStrength * 0.1 * (index + 1);
                        segment.rotation.z = Math.sin(this.time * 2 + index) * swayAmount;
                        segment.rotation.x = Math.cos(this.time * 1.5 + index) * swayAmount * 0.5;
                    });
                }
            });
        });
    }
    
    dispose() {
        // 清理资源
        [...this.willowTrees, ...this.buildings, ...this.bridges].forEach(obj => {
            this.scene.remove(obj);
            if (obj.geometry) obj.geometry.dispose();
            if (obj.material) obj.material.dispose();
        });
    }
}
